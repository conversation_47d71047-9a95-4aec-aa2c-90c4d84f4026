#!/usr/bin/env python
"""
PostgreSQL + pgvector Setup Script

This script sets up PostgreSQL with the pgvector extension and creates the necessary
tables and indexes for the RAG system.

Usage:
    python setup_pgvector.py [--force]

Options:
    --force    Force recreation of tables (drops existing tables)
"""

import asyncio
import argparse
import logging
import sys
from sqlalchemy import text

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Import database utilities
from app.core.db.database import get_db_context, init_db

async def setup_pgvector(force: bool = False):
    """
    Set up PostgreSQL with pgvector extension.
    
    Args:
        force: Whether to force recreation of tables
    """
    logger.info("Setting up PostgreSQL with pgvector extension")
    
    # Initialize database connection
    init_db()
    
    with get_db_context() as db:
        try:
            # Check if pgvector extension is installed
            result = db.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'"))
            if result.rowcount == 0:
                logger.info("Installing pgvector extension")
                db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
                db.commit()
                logger.info("pgvector extension installed successfully")
            else:
                logger.info("pgvector extension is already installed")
                
            # Drop tables if force is True
            if force:
                logger.warning("Dropping existing tables")
                db.execute(text("DROP TABLE IF EXISTS document_chunks"))
                db.execute(text("DROP TABLE IF EXISTS documents"))
                db.commit()
                
            # Create documents table
            logger.info("Creating documents table")
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS documents (
                    id VARCHAR(64) PRIMARY KEY,
                    title TEXT NOT NULL,
                    source TEXT,
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create document_chunks table with vector support
            logger.info("Creating document_chunks table with vector support")
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS document_chunks (
                    id VARCHAR(64) PRIMARY KEY,
                    document_id VARCHAR(64) REFERENCES documents(id) ON DELETE CASCADE,
                    content TEXT NOT NULL,
                    metadata JSONB,
                    embedding VECTOR(768),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create indexes
            logger.info("Creating indexes")
            
            # Full-text search index on document_chunks.content
            db.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_document_chunks_content ON document_chunks
                USING gin(to_tsvector('english', content))
            """))
            
            # HNSW index on document_chunks.embedding
            db.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON document_chunks
                USING hnsw (embedding vector_cosine_ops)
                WITH (ef_construction = 128, m = 16)
            """))
            
            # Metadata indexes
            db.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_documents_metadata ON documents
                USING gin(metadata)
            """))
            
            db.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_document_chunks_metadata ON document_chunks
                USING gin(metadata)
            """))
            
            db.commit()
            logger.info("PostgreSQL + pgvector setup completed successfully")
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error setting up PostgreSQL + pgvector: {e}")
            raise

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Set up PostgreSQL with pgvector extension")
    parser.add_argument("--force", action="store_true", help="Force recreation of tables")
    args = parser.parse_args()
    
    await setup_pgvector(force=args.force)

if __name__ == "__main__":
    asyncio.run(main())