#!/usr/bin/env python
"""
Configuration Validation Script

This script validates that the configuration system is working correctly
and all required environment variables are properly set.

Usage:
    python validate_config.py
"""

import sys
import logging
from pathlib import Path

# Add backend to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


def validate_configuration():
    """Validate the configuration system."""
    logger.info("🔧 Validating configuration system...")
    
    try:
        # Test configuration import
        from backend.app.config import settings, get_settings
        logger.info("✅ Configuration module imported successfully")
        
        # Test settings loading
        config = get_settings()
        logger.info("✅ Settings loaded successfully")
        
        # Display key configuration values
        logger.info(f"📊 Configuration Summary:")
        logger.info(f"  - DEBUG: {config.DEBUG}")
        logger.info(f"  - DEVELOPMENT_MODE: {config.DEVELOPMENT_MODE}")
        logger.info(f"  - LOG_LEVEL: {config.LOG_LEVEL}")
        logger.info(f"  - DATABASE_URL: {config.DATABASE_URL[:50]}...")
        logger.info(f"  - DEFAULT_EMBEDDING_MODEL: {config.DEFAULT_EMBEDDING_MODEL}")
        logger.info(f"  - VECTOR_STORE_TYPE: {config.VECTOR_STORE_TYPE}")
        logger.info(f"  - VECTOR_DIMENSION: {config.VECTOR_DIMENSION}")
        
        # Check API keys
        api_keys = {
            "OpenAI": config.OPENAI_API_KEY,
            "Anthropic": config.ANTHROPIC_API_KEY,
            "Google": config.GOOGLE_API_KEY,
        }
        
        available_keys = [name for name, key in api_keys.items() if key]
        if available_keys:
            logger.info(f"🔑 Available API keys: {', '.join(available_keys)}")
        else:
            logger.warning("⚠️  No API keys configured")
        
        # Validate configuration
        validation_errors = config.validate_api_keys()
        if validation_errors:
            logger.warning("⚠️  Configuration warnings:")
            for error in validation_errors:
                logger.warning(f"    - {error}")
        else:
            logger.info("✅ Configuration validation passed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False


def validate_database():
    """Validate database connection."""
    logger.info("🗄️  Validating database connection...")
    
    try:
        from backend.app.core.db.database import check_database_connection, init_db
        
        # Initialize database
        init_db()
        logger.info("✅ Database initialized successfully")
        
        # Check connection
        if check_database_connection():
            logger.info("✅ Database connection successful")
            return True
        else:
            logger.error("❌ Database connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database validation failed: {e}")
        return False


def validate_logging():
    """Validate logging system."""
    logger.info("📝 Validating logging system...")
    
    try:
        from backend.app.core.logging import setup_logging, get_logger
        
        # Test logger creation
        test_logger = get_logger("test_logger")
        test_logger.info("Test log message")
        logger.info("✅ Logging system working correctly")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Logging validation failed: {e}")
        return False


def main():
    """Main validation function."""
    logger.info("🚀 Starting configuration validation...")
    
    results = {
        "Configuration": validate_configuration(),
        "Database": validate_database(),
        "Logging": validate_logging(),
    }
    
    # Summary
    logger.info("\n📋 Validation Summary:")
    all_passed = True
    for component, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        logger.info(f"  - {component}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 All validations passed! Configuration system is ready.")
        return 0
    else:
        logger.error("\n💥 Some validations failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
