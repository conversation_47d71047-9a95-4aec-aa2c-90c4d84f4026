#!/usr/bin/env python
"""
Document Loading Script

This script loads documents from various sources into the knowledge base.

Usage:
    python load_documents.py [--source SOURCE] [--file FILE] [--chunk-size SIZE] [--overlap OVERLAP]

Options:
    --source SOURCE    Source type (file, directory, url) [default: file]
    --file FILE        Path to the file or directory to load
    --chunk-size SIZE  Size of document chunks in characters [default: 1000]
    --overlap OVERLAP  Overlap between chunks in characters [default: 200]
"""

import asyncio
import argparse
import json
import logging
import os
import sys
import uuid
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Import RAG components
from app.rag.embeddings import get_embedding_model
from app.rag.knowledge_base import KnowledgeBaseService, get_knowledge_base_service
from app.core.db.database import init_db

async def load_documents_from_file(
    file_path: str,
    chunk_size: int = 1000,
    overlap: int = 200
) -> List[Dict[str, Any]]:
    """
    Load documents from a file.
    
    Args:
        file_path: Path to the file
        chunk_size: Size of document chunks in characters
        overlap: Overlap between chunks in characters
        
    Returns:
        List of document dictionaries
    """
    logger.info(f"Loading documents from file: {file_path}")
    
    # Check file extension
    _, ext = os.path.splitext(file_path)
    ext = ext.lower()
    
    if ext == '.json':
        # Load JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # Handle different JSON formats
        if isinstance(data, list):
            # List of documents
            documents = data
        elif isinstance(data, dict) and 'documents' in data:
            # Dictionary with 'documents' key
            documents = data['documents']
        else:
            # Single document
            documents = [data]
            
        logger.info(f"Loaded {len(documents)} documents from JSON file")
        return documents
        
    elif ext in ['.txt', '.md', '.rst']:
        # Load text file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Create a single document
        document = {
            'id': str(uuid.uuid4()),
            'title': os.path.basename(file_path),
            'content': content,
            'metadata': {
                'source': file_path,
                'format': ext[1:],  # Remove the dot
            }
        }
        
        logger.info(f"Loaded text document from {file_path}")
        return [document]
        
    else:
        logger.error(f"Unsupported file format: {ext}")
        return []

async def chunk_document(
    document: Dict[str, Any],
    chunk_size: int = 1000,
    overlap: int = 200
) -> List[Dict[str, Any]]:
    """
    Split a document into chunks.
    
    Args:
        document: Document dictionary
        chunk_size: Size of chunks in characters
        overlap: Overlap between chunks in characters
        
    Returns:
        List of chunk dictionaries
    """
    content = document.get('content', '')
    if not content:
        return []
        
    # Simple chunking by characters
    chunks = []
    for i in range(0, len(content), chunk_size - overlap):
        chunk_content = content[i:i + chunk_size]
        if len(chunk_content) < 50:  # Skip very small chunks
            continue
            
        chunk = {
            'id': f"{document['id']}_chunk_{len(chunks)}",
            'document_id': document['id'],
            'content': chunk_content,
            'metadata': {
                **document.get('metadata', {}),
                'chunk_index': len(chunks),
                'document_title': document.get('title', '')
            }
        }
        chunks.append(chunk)
        
    logger.info(f"Split document '{document.get('title', '')}' into {len(chunks)} chunks")
    return chunks

async def load_and_process_documents(
    file_path: str,
    chunk_size: int = 1000,
    overlap: int = 200
):
    """
    Load, chunk, and store documents in the knowledge base.
    
    Args:
        file_path: Path to the file
        chunk_size: Size of document chunks in characters
        overlap: Overlap between chunks in characters
    """
    # Initialize database
    init_db()
    
    # Get knowledge base service
    knowledge_base = await get_knowledge_base_service()
    
    # Load documents
    documents = await load_documents_from_file(file_path, chunk_size, overlap)
    
    # Process each document
    for document in documents:
        try:
            # Add document to knowledge base
            doc_id = await knowledge_base.add_document(
                title=document.get('title', 'Untitled'),
                content=document.get('content', ''),
                metadata=document.get('metadata', {}),
                chunk_size=chunk_size,
                chunk_overlap=overlap
            )
            
            logger.info(f"Added document to knowledge base with ID: {doc_id}")
            
        except Exception as e:
            logger.error(f"Error processing document '{document.get('title', '')}': {e}")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Load documents into the knowledge base")
    parser.add_argument("--source", default="file", choices=["file", "directory", "url"],
                        help="Source type (file, directory, url)")
    parser.add_argument("--file", required=True, help="Path to the file or directory to load")
    parser.add_argument("--chunk-size", type=int, default=1000,
                        help="Size of document chunks in characters")
    parser.add_argument("--overlap", type=int, default=200,
                        help="Overlap between chunks in characters")
    args = parser.parse_args()
    
    if args.source == "file":
        await load_and_process_documents(args.file, args.chunk_size, args.overlap)
    elif args.source == "directory":
        # Process all files in the directory
        for filename in os.listdir(args.file):
            file_path = os.path.join(args.file, filename)
            if os.path.isfile(file_path):
                await load_and_process_documents(file_path, args.chunk_size, args.overlap)
    elif args.source == "url":
        logger.error("URL source not implemented yet")
    else:
        logger.error(f"Unknown source type: {args.source}")

if __name__ == "__main__":
    asyncio.run(main())