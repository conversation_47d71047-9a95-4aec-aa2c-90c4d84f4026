"""
Knowledge Base Service

This module implements a knowledge base service that works with PostgreSQL and pgvector
for storing and retrieving document embeddings.
"""

import logging
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple

from app.core.db.database import get_db_context
from app.core.db.models import Document
from app.config import get_settings
from app.rag.embeddings import get_embedding_model, EmbeddingModel
from app.rag.vector_store import get_vector_store

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    """
    Knowledge base service that works with PostgreSQL and pgvector.
    
    This service provides methods for:
    1. Storing documents with their embeddings
    2. Retrieving documents using vector search
    3. Retrieving documents using keyword search
    4. Retrieving documents using hybrid search (vector + keyword)
    """

    def __init__(
        self,
        vector_store=None,
        embedding_model=None,
        reranker=None,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        use_reranking: bool = False,
        reranking_candidates: int = 20
    ):
        """
        Initialize the knowledge base service.
        
        Args:
            vector_store: The vector store for embedding storage and retrieval
            embedding_model: The embedding model for vector conversion
            reranker: Optional reranker for improving search result relevance
            vector_weight: Weight for vector search results in hybrid search (0.0-1.0)
            keyword_weight: Weight for keyword search results in hybrid search (0.0-1.0)
            use_reranking: Whether to use reranking for search results
            reranking_candidates: Number of candidates to consider for reranking
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.reranker = reranker
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
        self.use_reranking = use_reranking
        self.reranking_candidates = reranking_candidates

    async def search(
        self,
        query: str,
        collection: str = None,  # Ignored, kept for compatibility
        limit: int = 5,
        search_type: str = "hybrid",
        filters: Optional[Dict[str, Any]] = None,
        vector_weight: Optional[float] = None,
        keyword_weight: Optional[float] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for documents matching the query.
        
        Args:
            query: The search query
            collection: Ignored, kept for compatibility
            limit: Maximum number of results to return
            search_type: Type of search to perform (vector, keyword, hybrid)
            filters: Metadata filters to apply
            vector_weight: Weight for vector search results (0.0-1.0)
            keyword_weight: Weight for keyword search results (0.0-1.0)
            user_id: Optional user ID for filtering
            thread_id: Optional thread ID for filtering
            session_id: Optional session ID for filtering
            department: Optional department for filtering
            
        Returns:
            List of matching documents with relevance scores
        """
        # Use provided weights or fall back to instance defaults
        vector_weight = vector_weight if vector_weight is not None else self.vector_weight
        keyword_weight = keyword_weight if keyword_weight is not None else self.keyword_weight
        
        # Normalize weights
        total_weight = vector_weight + keyword_weight
        if total_weight > 0:
            vector_weight = vector_weight / total_weight
            keyword_weight = keyword_weight / total_weight
            
        # Initialize filters if not provided
        filters = filters or {}
        
        # Add additional filters if provided
        if user_id:
            filters["user_id"] = user_id
        if thread_id:
            filters["thread_id"] = thread_id
        if session_id:
            filters["session_id"] = session_id
        if department:
            filters["department"] = department
            
        # Perform the appropriate search
        try:
            if search_type == "vector" and self.embedding_model and self.vector_store:
                # Perform vector search
                results = await self._vector_search(
                    query, limit, filters,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

                # Apply reranking if enabled
                if self.use_reranking and self.reranker and results:
                    try:
                        reranked_results = await self.reranker.rerank(
                            query=query,
                            results=results[:self.reranking_candidates],
                            top_n=limit
                        )
                        if reranked_results:
                            results = reranked_results
                    except Exception as e:
                        logger.error(f"Error during vector search reranking: {e}")
                        # Continue with original results
                        
            elif search_type == "keyword":
                # Perform keyword search
                results = await self._keyword_search(
                    query, limit, filters,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )
                
            elif search_type == "hybrid":
                # Perform hybrid search
                results = await self._hybrid_search(
                    query, limit, filters,
                    vector_weight=vector_weight,
                    keyword_weight=keyword_weight,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )
                
                # Apply reranking if enabled
                if self.use_reranking and self.reranker and results:
                    try:
                        reranked_results = await self.reranker.rerank(
                            query=query,
                            results=results[:self.reranking_candidates],
                            top_n=limit
                        )
                        if reranked_results:
                            results = reranked_results
                    except Exception as e:
                        logger.error(f"Error during hybrid search reranking: {e}")
                        # Continue with original results
            else:
                logger.warning(f"Unknown search type: {search_type}, falling back to hybrid search")
                results = await self._hy