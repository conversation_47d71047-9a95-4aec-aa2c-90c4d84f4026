"""
Knowledge Base Service

This module implements a knowledge base service that works with PostgreSQL and pgvector
for storing and retrieving document embeddings.
"""

import logging
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple

from app.core.db.database import get_db_context
from app.core.db.models import Document
from app.config import get_settings
from .embeddings import get_embedding_model, EmbeddingModel
from .vector_store import get_vector_store

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    """
    Knowledge base service that works with PostgreSQL and pgvector.

    This service provides methods for:
    1. Storing documents with their embeddings
    2. Retrieving documents using vector search
    3. Retrieving documents using keyword search
    4. Retrieving documents using hybrid search (vector + keyword)
    """

    def __init__(
        self,
        vector_store=None,
        embedding_model=None,
        reranker=None,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        use_reranking: bool = False,
        reranking_candidates: int = 20
    ):
        """
        Initialize the knowledge base service.

        Args:
            vector_store: The vector store for embedding storage and retrieval
            embedding_model: The embedding model for vector conversion
            reranker: Optional reranker for improving search result relevance
            vector_weight: Weight for vector search results in hybrid search (0.0-1.0)
            keyword_weight: Weight for keyword search results in hybrid search (0.0-1.0)
            use_reranking: Whether to use reranking for search results
            reranking_candidates: Number of candidates to consider for reranking
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.reranker = reranker
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
        self.use_reranking = use_reranking
        self.reranking_candidates = reranking_candidates

    async def search(
        self,
        query: str,
        collection: str = None,  # Ignored, kept for compatibility
        limit: int = 5,
        search_type: str = "hybrid",
        filters: Optional[Dict[str, Any]] = None,
        vector_weight: Optional[float] = None,
        keyword_weight: Optional[float] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for documents matching the query.

        Args:
            query: The search query
            collection: Ignored, kept for compatibility
            limit: Maximum number of results to return
            search_type: Type of search to perform (vector, keyword, hybrid)
            filters: Metadata filters to apply
            vector_weight: Weight for vector search results (0.0-1.0)
            keyword_weight: Weight for keyword search results (0.0-1.0)
            user_id: Optional user ID for filtering
            thread_id: Optional thread ID for filtering
            session_id: Optional session ID for filtering
            department: Optional department for filtering

        Returns:
            List of matching documents with relevance scores
        """
        # Use provided weights or fall back to instance defaults
        vector_weight = vector_weight if vector_weight is not None else self.vector_weight
        keyword_weight = keyword_weight if keyword_weight is not None else self.keyword_weight

        # Normalize weights
        total_weight = vector_weight + keyword_weight
        if total_weight > 0:
            vector_weight = vector_weight / total_weight
            keyword_weight = keyword_weight / total_weight

        # Initialize filters if not provided
        filters = filters or {}

        # Add additional filters if provided
        if user_id:
            filters["user_id"] = user_id
        if thread_id:
            filters["thread_id"] = thread_id
        if session_id:
            filters["session_id"] = session_id
        if department:
            filters["department"] = department

        # Perform the appropriate search
        try:
            if search_type == "vector" and self.embedding_model and self.vector_store:
                # Perform vector search
                results = await self._vector_search(
                    query, limit, filters,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

                # Apply reranking if enabled
                if self.use_reranking and self.reranker and results:
                    try:
                        reranked_results = await self.reranker.rerank(
                            query=query,
                            results=results[:self.reranking_candidates],
                            top_n=limit
                        )
                        if reranked_results:
                            results = reranked_results
                    except Exception as e:
                        logger.error(f"Error during vector search reranking: {e}")
                        # Continue with original results

            elif search_type == "keyword":
                # Perform keyword search
                results = await self._keyword_search(
                    query, limit, filters,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

            elif search_type == "hybrid":
                # Perform hybrid search
                results = await self._hybrid_search(
                    query, limit, filters,
                    vector_weight=vector_weight,
                    keyword_weight=keyword_weight,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

                # Apply reranking if enabled
                if self.use_reranking and self.reranker and results:
                    try:
                        reranked_results = await self.reranker.rerank(
                            query=query,
                            results=results[:self.reranking_candidates],
                            top_n=limit
                        )
                        if reranked_results:
                            results = reranked_results
                    except Exception as e:
                        logger.error(f"Error during hybrid search reranking: {e}")
                        # Continue with original results
            else:
                logger.warning(f"Unknown search type: {search_type}, falling back to hybrid search")
                results = await self._hybrid_search(
                    query, limit, filters,
                    vector_weight=vector_weight,
                    keyword_weight=keyword_weight,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

            return results

        except Exception as e:
            logger.error(f"Search failed: {e}", exc_info=True)
            return []

    async def _vector_search(
        self,
        query: str,
        limit: int,
        filters: Dict[str, Any],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Perform vector similarity search."""
        if not self.embedding_model or not self.vector_store:
            logger.warning("Vector search requires embedding model and vector store")
            return []

        try:
            # Generate query embedding
            query_embedding = await self.embedding_model.embed_query(query)

            # Perform vector search
            results = await self.vector_store.search(
                query_embedding=query_embedding,
                limit=limit,
                filters=filters
            )

            return results

        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []

    async def _keyword_search(
        self,
        query: str,
        limit: int,
        filters: Dict[str, Any],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Perform keyword search using PostgreSQL full-text search."""
        try:
            # Build the search query
            search_query = f"""
                SELECT
                    id,
                    content,
                    metadata,
                    ts_rank(to_tsvector('english', content), plainto_tsquery('english', :query)) AS score
                FROM document_chunks
                WHERE to_tsvector('english', content) @@ plainto_tsquery('english', :query)
            """

            # Add filters
            filter_conditions = []
            params = {"query": query}

            if filters:
                for key, value in filters.items():
                    if isinstance(value, dict):
                        if "$eq" in value:
                            filter_conditions.append(f"metadata->>{key!r} = :{key}")
                            params[key] = value["$eq"]
                        elif "$in" in value:
                            placeholders = ",".join(f":{key}_{i}" for i in range(len(value["$in"])))
                            filter_conditions.append(f"metadata->>{key!r} IN ({placeholders})")
                            for i, val in enumerate(value["$in"]):
                                params[f"{key}_{i}"] = val
                    else:
                        filter_conditions.append(f"metadata->>{key!r} = :{key}")
                        params[key] = value

            if filter_conditions:
                search_query += " AND " + " AND ".join(filter_conditions)

            search_query += f" ORDER BY score DESC LIMIT {limit}"

            # Execute search
            with get_db_context() as db:
                result = db.execute(text(search_query), params)
                rows = result.fetchall()

                # Convert to expected format
                results = []
                for row in rows:
                    metadata = json.loads(row.metadata) if row.metadata else {}
                    results.append({
                        "id": row.id,
                        "text": row.content,
                        "metadata": metadata,
                        "score": float(row.score)
                    })

                return results

        except Exception as e:
            logger.error(f"Keyword search failed: {e}")
            return []

    async def _hybrid_search(
        self,
        query: str,
        limit: int,
        filters: Dict[str, Any],
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Perform hybrid search combining vector and keyword search."""
        try:
            # Perform both searches in parallel
            vector_results = await self._vector_search(query, limit * 2, filters, **kwargs)
            keyword_results = await self._keyword_search(query, limit * 2, filters, **kwargs)

            # Combine and rerank results
            combined_results = {}

            # Add vector results
            for result in vector_results:
                doc_id = result["id"]
                combined_results[doc_id] = {
                    **result,
                    "vector_score": result["score"],
                    "keyword_score": 0.0
                }

            # Add keyword results
            for result in keyword_results:
                doc_id = result["id"]
                if doc_id in combined_results:
                    combined_results[doc_id]["keyword_score"] = result["score"]
                else:
                    combined_results[doc_id] = {
                        **result,
                        "vector_score": 0.0,
                        "keyword_score": result["score"]
                    }

            # Calculate hybrid scores
            for doc_id, result in combined_results.items():
                # Normalize scores (simple min-max normalization)
                vector_score = result["vector_score"]
                keyword_score = result["keyword_score"]

                # Combine scores
                hybrid_score = (vector_weight * vector_score) + (keyword_weight * keyword_score)
                result["score"] = hybrid_score

            # Sort by hybrid score and return top results
            sorted_results = sorted(
                combined_results.values(),
                key=lambda x: x["score"],
                reverse=True
            )

            # Clean up extra score fields
            final_results = []
            for result in sorted_results[:limit]:
                clean_result = {
                    "id": result["id"],
                    "text": result["text"],
                    "metadata": result["metadata"],
                    "score": result["score"]
                }
                final_results.append(clean_result)

            return final_results

        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []