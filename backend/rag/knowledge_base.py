"""
Knowledge Base Service - Simplified

This module implements a knowledge base service that works with PostgreSQL and pgvector
for storing and retrieving document embeddings. Focuses on core functionality only.
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from sqlalchemy import text

from app.core.db.database import get_db_context
from app.config import get_settings

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    """
    Simplified knowledge base service using PostgreSQL and pgvector.

    Provides core functionality for:
    1. Storing documents with embeddings
    2. Vector similarity search
    3. Keyword search
    4. Hybrid search (vector + keyword)
    """

    def __init__(
        self,
        embedding_model,
        table_name: str = "document_chunks",
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3
    ):
        """
        Initialize the knowledge base service.

        Args:
            embedding_model: The embedding model for vector conversion
            table_name: Name of the database table for documents
            vector_weight: Weight for vector search in hybrid search (0.0-1.0)
            keyword_weight: Weight for keyword search in hybrid search (0.0-1.0)
        """
        self.embedding_model = embedding_model
        self.table_name = table_name
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight

        # Normalize weights
        total_weight = vector_weight + keyword_weight
        if total_weight > 0:
            self.vector_weight = vector_weight / total_weight
            self.keyword_weight = keyword_weight / total_weight

    async def search(
        self,
        query: str,
        limit: int = 5,
        search_type: str = "hybrid",
        filters: Optional[Dict[str, Any]] = None,
        **kwargs  # For compatibility with existing code
    ) -> List[Dict[str, Any]]:
        """
        Search for documents matching the query.

        Args:
            query: The search query
            limit: Maximum number of results to return
            search_type: Type of search ("vector", "keyword", "hybrid")
            filters: Optional metadata filters
            **kwargs: Additional parameters for compatibility

        Returns:
            List of documents with relevance scores
        """
        try:
            if search_type == "vector":
                return await self._vector_search(query, limit, filters)
            elif search_type == "keyword":
                return await self._keyword_search(query, limit, filters)
            else:  # hybrid (default)
                return await self._hybrid_search(query, limit, filters)
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    async def add_documents(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add documents to the knowledge base.

        Args:
            texts: List of document texts
            metadatas: Optional list of metadata dicts
            ids: Optional list of document IDs

        Returns:
            List of document IDs
        """
        import uuid

        if ids is None:
            ids = [str(uuid.uuid4()) for _ in texts]
        if metadatas is None:
            metadatas = [{} for _ in texts]

        # Generate embeddings
        embeddings = await self.embedding_model.embed_documents(texts)

        # Store in database
        with get_db_context() as db:
            for doc_id, text, metadata, embedding in zip(ids, texts, metadatas, embeddings):
                vector_str = f"[{','.join(str(x) for x in embedding)}]"

                db.execute(
                    text(f"""
                        INSERT INTO {self.table_name} (id, content, metadata, embedding)
                        VALUES (:id, :content, :metadata, :embedding::vector)
                        ON CONFLICT (id) DO UPDATE
                        SET content = :content, metadata = :metadata, embedding = :embedding::vector
                    """),
                    {
                        "id": doc_id,
                        "content": text,
                        "metadata": json.dumps(metadata),
                        "embedding": vector_str
                    }
                )
            db.commit()

        logger.info(f"Added {len(texts)} documents to knowledge base")
        return ids

    async def _vector_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Perform vector similarity search."""
        # Generate query embedding
        query_embedding = await self.embedding_model.embed_query(query)
        vector_str = f"[{','.join(str(x) for x in query_embedding)}]"

        # Build filter conditions
        filter_conditions = []
        params = {"query_vector": vector_str, "limit": limit}

        if filters:
            for key, value in filters.items():
                filter_conditions.append(f"metadata->>{key!r} = :{key}")
                params[key] = str(value)

        where_clause = f"WHERE {' AND '.join(filter_conditions)}" if filter_conditions else ""

        # Execute vector search
        with get_db_context() as db:
            result = db.execute(
                text(f"""
                    SELECT id, content, metadata,
                           1 - (embedding <=> :query_vector::vector) as score
                    FROM {self.table_name}
                    {where_clause}
                    ORDER BY embedding <=> :query_vector::vector
                    LIMIT :limit
                """),
                params
            )

            return [
                {
                    "id": row.id,
                    "text": row.content,
                    "metadata": json.loads(row.metadata) if row.metadata else {},
                    "score": float(row.score)
                }
                for row in result
            ]

    async def _keyword_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Perform keyword search using PostgreSQL full-text search."""
        # Build filter conditions
        filter_conditions = []
        params = {"query": query, "limit": limit}

        if filters:
            for key, value in filters.items():
                filter_conditions.append(f"metadata->>{key!r} = :{key}")
                params[key] = str(value)

        where_clause = f"AND {' AND '.join(filter_conditions)}" if filter_conditions else ""

        # Execute keyword search
        with get_db_context() as db:
            result = db.execute(
                text(f"""
                    SELECT id, content, metadata,
                           ts_rank(to_tsvector('english', content), plainto_tsquery('english', :query)) as score
                    FROM {self.table_name}
                    WHERE to_tsvector('english', content) @@ plainto_tsquery('english', :query)
                    {where_clause}
                    ORDER BY score DESC
                    LIMIT :limit
                """),
                params
            )

            return [
                {
                    "id": row.id,
                    "text": row.content,
                    "metadata": json.loads(row.metadata) if row.metadata else {},
                    "score": float(row.score)
                }
                for row in result
            ]

    async def _hybrid_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Perform hybrid search combining vector and keyword results."""
        # Get results from both search types
        vector_results = await self._vector_search(query, limit * 2, filters)
        keyword_results = await self._keyword_search(query, limit * 2, filters)

        # Combine and rerank results
        combined_results = {}

        # Add vector results with weight
        for result in vector_results:
            doc_id = result["id"]
            combined_results[doc_id] = {
                **result,
                "score": result["score"] * self.vector_weight
            }

        # Add keyword results with weight
        for result in keyword_results:
            doc_id = result["id"]
            if doc_id in combined_results:
                # Combine scores
                combined_results[doc_id]["score"] += result["score"] * self.keyword_weight
            else:
                combined_results[doc_id] = {
                    **result,
                    "score": result["score"] * self.keyword_weight
                }

        # Sort by combined score and return top results
        sorted_results = sorted(
            combined_results.values(),
            key=lambda x: x["score"],
            reverse=True
        )

        return sorted_results[:limit]