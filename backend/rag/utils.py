"""
RAG Utilities - Simplified

This module provides utility functions and configuration for the RAG system.
"""

import logging
from typing import Optional

from app.config import get_settings

logger = logging.getLogger(__name__)


class RAGConfig:
    """
    Simple configuration class for RAG components.
    """

    def __init__(self, **kwargs):
        """
        Initialize RAG configuration.

        Args:
            **kwargs: Configuration options
        """
        settings = get_settings()

        # Embedding configuration
        self.embedding_provider = kwargs.get("embedding_provider", "openai")
        self.embedding_model = kwargs.get("embedding_model", "text-embedding-3-small")

        # Search weights
        self.vector_weight = kwargs.get("vector_weight", 0.7)
        self.keyword_weight = kwargs.get("keyword_weight", 0.3)


async def initialize_rag_system(config: Optional[RAGConfig] = None):
    """
    Initialize the complete RAG system.

    Args:
        config: RAG configuration

    Returns:
        Tuple of (embedding_model, knowledge_base_service, retriever)
    """
    if config is None:
        config = RAGConfig()

    # Import here to avoid circular imports
    from .embeddings import get_embedding_model
    from .knowledge_base import KnowledgeBaseService
    from .retriever import Retriever

    logger.info("Initializing RAG system")

    # Initialize embedding model
    embedding_model = await get_embedding_model(
        provider=config.embedding_provider,
        model_name=config.embedding_model,
        fallback=True
    )

    # Initialize knowledge base service
    knowledge_base_service = KnowledgeBaseService(
        embedding_model=embedding_model,
        vector_weight=config.vector_weight,
        keyword_weight=config.keyword_weight
    )

    # Initialize retriever
    retriever = Retriever(knowledge_base_service)

    logger.info("RAG system initialized successfully")

    return embedding_model, knowledge_base_service, retriever