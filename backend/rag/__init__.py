"""
RAG Package

This package contains the implementation of the RAG (Retrieval-Augmented Generation) system:
- Embeddings: Convert text to vector representations
- Vector Store: Store and retrieve vectors efficiently
- Retriever: Process queries and retrieve relevant documents
- Generator: Combine retrieved context with LLM generation
- Knowledge Base: Service for accessing the knowledge base
- Query Analyzer: Analyze queries for intent and relevant departments
- Timeout: Timeout and retry utilities for RAG operations
"""

# Import existing components
from .pgvector_knowledge_base import PgVectorKnowledgeBaseService
from .query_analyzer import QueryAnalyzer

# Import new components
from .embeddings import EmbeddingModel, HuggingFaceEmbedding, OpenAIEmbedding, EmbeddingFactory
from .vector_store import VectorStore, FAISSVectorStore, ChromaVectorStore, get_vector_store
from .retriever import Retriever, HybridRetriever, ContextWindowManager, QueryRewriter
from .generator import Generator, RAGGenerator
from .utils import initialize_embedding_model, initialize_knowledge_base_service, EnhancedMockEmbeddingModel
from .timeout import (
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout,
    with_embedding_timeout_decorator,
    with_vector_search_timeout_decorator,
    with_keyword_search_timeout_decorator,
    with_hybrid_search_timeout_decorator
)

__all__ = [
    # Core RAG components
    "PgVectorKnowledgeBaseService",
    "QueryAnalyzer",
    "EmbeddingModel",
    "HuggingFaceEmbedding",
    "OpenAIEmbedding",
    "EmbeddingFactory",
    "VectorStore",
    "FAISSVectorStore",
    "ChromaVectorStore",
    "get_vector_store",
    "Retriever",
    "HybridRetriever",
    "ContextWindowManager",
    "QueryRewriter",
    "Generator",
    "RAGGenerator",

    # Utility functions
    "initialize_embedding_model",
    "initialize_knowledge_base_service",
    "EnhancedMockEmbeddingModel",

    # Timeout utilities
    "with_embedding_timeout",
    "with_vector_search_timeout",
    "with_rag_timeout",
    "with_embedding_timeout_decorator",
    "with_vector_search_timeout_decorator",
    "with_keyword_search_timeout_decorator",
    "with_hybrid_search_timeout_decorator"
]
