"""
RAG Package - Simplified Implementation

This package contains the core RAG (Retrieval-Augmented Generation) system:
- Embeddings: Convert text to vector representations (OpenAI/HuggingFace)
- Knowledge Base: Store and retrieve documents with pgvector
- Retriever: Process queries and retrieve relevant documents
"""

# Core RAG components
from .embeddings import EmbeddingModel, HuggingFaceEmbedding, OpenAIEmbedding, get_embedding_model
from .knowledge_base import KnowledgeBaseService
from .retriever import Retriever
from .utils import RAGConfig, initialize_rag_system

__all__ = [
    # Core components
    "EmbeddingModel",
    "HuggingFaceEmbedding",
    "OpenAIEmbedding",
    "get_embedding_model",
    "KnowledgeBaseService",
    "Retriever",
    "RAGConfig",
    "initialize_rag_system"
]
