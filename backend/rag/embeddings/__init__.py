"""
Embedding Models Package - Simplified

This package provides embedding models for converting text to vector representations.
Supports OpenAI and HuggingFace models with automatic fallback.
"""

from .base import EmbeddingModel
from .huggingface import HuggingFaceEmbedding
from .openai import OpenAIEmbedding

# Check for available dependencies
try:
    import sentence_transformers
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    HUGGINGFACE_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False


async def get_embedding_model(
    provider: str = "openai",
    model_name: str = None,
    fallback: bool = True,
    **kwargs
) -> EmbeddingModel:
    """
    Get an embedding model with automatic fallback.

    Args:
        provider: "openai" or "huggingface"
        model_name: Specific model name (optional)
        fallback: Enable automatic fallback to other provider
        **kwargs: Additional model parameters

    Returns:
        Initialized embedding model
    """
    import logging
    logger = logging.getLogger(__name__)

    # Default model names
    if model_name is None:
        if provider == "openai":
            model_name = "text-embedding-3-small"
        else:  # huggingface
            model_name = "intfloat/e5-base-v2"

    # Try primary provider
    try:
        if provider == "openai" and OPENAI_AVAILABLE:
            return OpenAIEmbedding(model_name=model_name, **kwargs)
        elif provider == "huggingface" and HUGGINGFACE_AVAILABLE:
            return HuggingFaceEmbedding(model_name=model_name, **kwargs)
        else:
            raise ImportError(f"{provider} dependencies not available")
    except Exception as e:
        if not fallback:
            raise e

        logger.warning(f"Failed to initialize {provider} embedding: {e}")

        # Try fallback
        if provider == "openai" and HUGGINGFACE_AVAILABLE:
            logger.info("Falling back to HuggingFace embedding")
            return HuggingFaceEmbedding(model_name="intfloat/e5-base-v2", **kwargs)
        elif provider == "huggingface" and OPENAI_AVAILABLE:
            logger.info("Falling back to OpenAI embedding")
            return OpenAIEmbedding(model_name="text-embedding-3-small", **kwargs)
        else:
            raise ImportError("No embedding providers available")


__all__ = [
    "EmbeddingModel",
    "HuggingFaceEmbedding",
    "OpenAIEmbedding",
    "get_embedding_model",
    "HUGGINGFACE_AVAILABLE",
    "OPENAI_AVAILABLE"
]